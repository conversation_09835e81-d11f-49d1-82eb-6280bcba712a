{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:08:03","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:08:03","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:08:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:09:42","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:09:42","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:09:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:10:16","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:10:16","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:10:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 18:29:40","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 18:29:40","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 18:29:40","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 18:29:40","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 18:29:40","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 18:29:40","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:29:44","userEmail":"<EMAIL>","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:29:44","userEmail":"<EMAIL>","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:29:44","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:29:44","useMockModel":true,"version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:29:44","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-18 18:29:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Analysis result saved successfully","resultId":"f279af88-39ed-4b14-a99d-bb215ff279db","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Analysis result saved to Archive Service","resultId":"f279af88-39ed-4b14-a99d-bb215ff279db","service":"analysis-worker","timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Analysis complete notification sent","resultId":"f279af88-39ed-4b14-a99d-bb215ff279db","service":"analysis-worker","timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Analysis completion notification sent","resultId":"f279af88-39ed-4b14-a99d-bb215ff279db","service":"analysis-worker","timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"jobId":"a8055cfc-a4c0-4daa-8704-ddd1bb23af45","level":"info","message":"Assessment job processed successfully","processingTime":"1890ms","resultId":"f279af88-39ed-4b14-a99d-bb215ff279db","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:29:46","userId":"61311cc7-9647-428e-b68c-9f5c3959569e","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 18:30:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 18:30:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 18:31:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 18:31:40","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","userEmail":"<EMAIL>","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","useMockModel":true,"version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:57","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:58","userEmail":"<EMAIL>","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","userEmail":"<EMAIL>","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:58","userEmail":"<EMAIL>","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","userEmail":"<EMAIL>","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-18 18:31:58","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-18 18:31:59","version":"1.0.0","weaknessesCount":3}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Analysis result saved successfully","resultId":"58f0761b-fd00-4dd8-9529-59ca785c6e66","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Analysis result saved to Archive Service","resultId":"58f0761b-fd00-4dd8-9529-59ca785c6e66","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-18 18:31:59","version":"1.0.0","weaknessesCount":3}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Analysis complete notification sent","resultId":"58f0761b-fd00-4dd8-9529-59ca785c6e66","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Analysis completion notification sent","resultId":"58f0761b-fd00-4dd8-9529-59ca785c6e66","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"ea6b1883-98f4-4981-b18a-e1834f612167","level":"info","message":"Assessment job processed successfully","processingTime":"1207ms","resultId":"58f0761b-fd00-4dd8-9529-59ca785c6e66","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"2fd69a35-509a-42dc-ad9f-ac8975e9c0a8","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Analysis result saved successfully","resultId":"2e0bcd16-6410-46ce-8d30-f600de345453","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Analysis result saved to Archive Service","resultId":"2e0bcd16-6410-46ce-8d30-f600de345453","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Analysis complete notification sent","resultId":"2e0bcd16-6410-46ce-8d30-f600de345453","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Analysis completion notification sent","resultId":"2e0bcd16-6410-46ce-8d30-f600de345453","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"jobId":"4af259cb-06b7-4a88-9b19-f8e62c17ccd7","level":"info","message":"Assessment job processed successfully","processingTime":"1398ms","resultId":"2e0bcd16-6410-46ce-8d30-f600de345453","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"207f753e-7701-4eaa-b65b-a726adbdae27","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-18 18:31:59","version":"1.0.0","weaknessesCount":3}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Analysis result saved successfully","resultId":"57d2fee7-6747-4f8d-8a66-21aed1cd6837","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Analysis result saved to Archive Service","resultId":"57d2fee7-6747-4f8d-8a66-21aed1cd6837","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Analysis complete notification sent","resultId":"57d2fee7-6747-4f8d-8a66-21aed1cd6837","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Analysis completion notification sent","resultId":"57d2fee7-6747-4f8d-8a66-21aed1cd6837","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"jobId":"8169cb13-9b74-4c58-83b8-0c2a09179807","level":"info","message":"Assessment job processed successfully","processingTime":"1414ms","resultId":"57d2fee7-6747-4f8d-8a66-21aed1cd6837","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"7a57a768-d978-4975-beb3-f34f7cf334eb","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-18 18:31:59","version":"1.0.0","weaknessesCount":3}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Analysis result saved successfully","resultId":"054298af-45d4-40b6-b153-601309cf0201","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Analysis result saved to Archive Service","resultId":"054298af-45d4-40b6-b153-601309cf0201","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Analysis complete notification sent","resultId":"054298af-45d4-40b6-b153-601309cf0201","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Analysis completion notification sent","resultId":"054298af-45d4-40b6-b153-601309cf0201","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"jobId":"e9fa27d9-f0a0-495f-8ca1-9aa683dab7b5","level":"info","message":"Assessment job processed successfully","processingTime":"1627ms","resultId":"054298af-45d4-40b6-b153-601309cf0201","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"00f44a33-1101-4ea4-87cb-0886a2ee1540","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-18 18:31:59","version":"1.0.0","weaknessesCount":3}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Analysis result saved successfully","resultId":"651d1661-30d1-43fe-87eb-3664bbb6e964","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Analysis result saved to Archive Service","resultId":"651d1661-30d1-43fe-87eb-3664bbb6e964","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Analysis complete notification sent","resultId":"651d1661-30d1-43fe-87eb-3664bbb6e964","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Analysis completion notification sent","resultId":"651d1661-30d1-43fe-87eb-3664bbb6e964","service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"jobId":"b5e1665a-91d7-4629-a4aa-f47a0a8bcb85","level":"info","message":"Assessment job processed successfully","processingTime":"1752ms","resultId":"651d1661-30d1-43fe-87eb-3664bbb6e964","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:31:59","userId":"55ea876d-9687-499c-ad1e-099b1e2b2e27","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-18 18:32:00","version":"1.0.0","weaknessesCount":3}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Analysis result saved successfully","resultId":"b75b92ba-2929-47c1-9b55-48f126a19b87","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Analysis result saved to Archive Service","resultId":"b75b92ba-2929-47c1-9b55-48f126a19b87","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Analysis complete notification sent","resultId":"b75b92ba-2929-47c1-9b55-48f126a19b87","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Analysis completion notification sent","resultId":"b75b92ba-2929-47c1-9b55-48f126a19b87","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"jobId":"eca1cecd-8ef1-43ef-b6c9-4541ec3a7aed","level":"info","message":"Assessment job processed successfully","processingTime":"2252ms","resultId":"b75b92ba-2929-47c1-9b55-48f126a19b87","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"302e9263-27e3-40f4-94eb-0e4141f4606c","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-18 18:32:00","version":"1.0.0","weaknessesCount":3}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Analysis result saved successfully","resultId":"83017265-a229-48a4-972d-82132e30e1b3","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Analysis result saved to Archive Service","resultId":"83017265-a229-48a4-972d-82132e30e1b3","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Analysis complete notification sent","resultId":"83017265-a229-48a4-972d-82132e30e1b3","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Analysis completion notification sent","resultId":"83017265-a229-48a4-972d-82132e30e1b3","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"jobId":"2ea9d7a7-9978-42a2-b978-8957aeded3d0","level":"info","message":"Assessment job processed successfully","processingTime":"2169ms","resultId":"83017265-a229-48a4-972d-82132e30e1b3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"d5391a40-4b2b-43dd-bb46-26a5d4bb566d","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-18 18:32:00","version":"1.0.0","weaknessesCount":3}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Analysis result saved successfully","resultId":"7b761be6-bcaa-4e78-884b-420526486db7","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Analysis result saved to Archive Service","resultId":"7b761be6-bcaa-4e78-884b-420526486db7","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Analysis complete notification sent","resultId":"7b761be6-bcaa-4e78-884b-420526486db7","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Analysis completion notification sent","resultId":"7b761be6-bcaa-4e78-884b-420526486db7","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"jobId":"2f6a0fea-39dc-4052-82a0-ef912795c8d4","level":"info","message":"Assessment job processed successfully","processingTime":"3014ms","resultId":"7b761be6-bcaa-4e78-884b-420526486db7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"3b0c5362-1b27-43b9-a00d-7c4977828742","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-18 18:32:00","version":"1.0.0","weaknessesCount":3}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Analysis result saved successfully","resultId":"1399a943-732c-4ba2-a92e-5c2500eb00a0","service":"analysis-worker","status":201,"timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Analysis result saved to Archive Service","resultId":"1399a943-732c-4ba2-a92e-5c2500eb00a0","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Analysis complete notification sent","resultId":"1399a943-732c-4ba2-a92e-5c2500eb00a0","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Analysis completion notification sent","resultId":"1399a943-732c-4ba2-a92e-5c2500eb00a0","service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"jobId":"22da5599-894c-419b-810c-3fc8f9047b5b","level":"info","message":"Assessment job processed successfully","processingTime":"2975ms","resultId":"1399a943-732c-4ba2-a92e-5c2500eb00a0","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 18:32:00","userId":"0322e8c6-c295-4c67-b7e9-a4d73be9e20c","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 18:32:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 18:32:40","version":"1.0.0"}
